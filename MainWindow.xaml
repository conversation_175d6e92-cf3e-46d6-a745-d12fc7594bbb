﻿<Window x:Class="Yidev.LocalAI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Yidev.LocalAI"
        xmlns:models="clr-namespace:Yidev.LocalAI.Models"
        xmlns:markdig="clr-namespace:Markdig.Wpf;assembly=Markdig.Wpf"
        mc:Ignorable="d"
        Title="本地AI聊天" Height="800" Width="1200" MinHeight="600" MinWidth="900"
        Background="#F5F6FA" WindowStartupLocation="CenterScreen"
        FontFamily="Microsoft YaHei UI, Microsoft YaHei, Segoe UI">
    <Window.Resources>
        <local:BoolToColorConverter x:Key="BoolToColorConverter" 
                          UserColor="#1E88E5" 
                          AIColor="#26A69A"/>
        <local:BoolToTextColorConverter x:Key="BoolToTextColorConverter"
                             UserTextColor="White"
                             AITextColor="White"/>
        <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <local:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <local:BoolToAlignmentConverter x:Key="BoolToAlignmentConverter"/>
        <local:InverseBoolToAlignmentConverter x:Key="InverseBoolToAlignmentConverter"/>
        <local:BoolToVisibilityConverter2 x:Key="BoolToVisibilityConverter2"/>
        <local:InverseBoolToVisibilityConverter2 x:Key="InverseBoolToVisibilityConverter2"/>
        <local:DateTimeToRelativeTimeConverter x:Key="DateTimeToRelativeTimeConverter"/>

        <Style x:Key="ChatBubbleText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,2,0,2"/>
        </Style>
        <Style x:Key="RoundButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="25" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1565C0" TargetName="border"/>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 动画故事板 -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Duration="0:0:0.3" To="250" Storyboard.TargetProperty="Width">
                <DoubleAnimation.EasingFunction>
                    <QuinticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="SlideOutAnimation">
            <DoubleAnimation Duration="0:0:0.3" To="0" Storyboard.TargetProperty="Width">
                <DoubleAnimation.EasingFunction>
                    <QuinticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>


        <!-- 加载动画故事板 -->
        <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Duration="0:0:1" From="0" To="360" 
                             Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"/>
        </Storyboard>

        <!-- 打字机效果动画 -->
        <Storyboard x:Key="TypingAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Duration="0:0:0.5" From="0" To="1" AutoReverse="True"
                             Storyboard.TargetProperty="Opacity"/>
        </Storyboard>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                CornerRadius="25" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.3" Color="#667eea"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#26A69A" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 聊天消息操作按钮样式 -->
        <Style x:Key="ChatActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                CornerRadius="16" 
                                Background="{TemplateBinding Background}"
                                Width="32" Height="32">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#495057" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#343A40" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="IconButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                CornerRadius="16" 
                                Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#30FFFFFF" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#50FFFFFF" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernListBoxItemStyle" TargetType="ListBoxItem">
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border x:Name="Bd" 
                                CornerRadius="12" 
                                Background="{TemplateBinding Background}" 
                                Padding="16,12"
                                Margin="4,2">
                            <ContentPresenter HorizontalAlignment="Stretch" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="true">
                                <Setter Property="Background" Value="#4A90E2" TargetName="Bd"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter Property="Background" Value="#E8F4FD" TargetName="Bd"/>
                                <Setter Property="Foreground" Value="#2C3E50"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FAFBFC"/>
            <Setter Property="BorderBrush" Value="#E1E8ED"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border" 
                                CornerRadius="20"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Padding="{TemplateBinding Padding}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#4A90E2" TargetName="border"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#667eea" TargetName="border"/>
                                <Setter Property="Background" Value="White" TargetName="border"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

    </Window.Resources>

    <Border CornerRadius="10" Margin="5" Background="White">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="1" BlurRadius="10" Opacity="0.2"/>
        </Border.Effect>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition x:Name="SidebarColumn" Width="280" MinWidth="0" MaxWidth="400"/>
                <ColumnDefinition Width="8"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧话题列表 -->
            <Border x:Name="SidebarBorder" Grid.Column="0" CornerRadius="15,0,0,15" ClipToBounds="True">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#F8F9FA" Offset="0"/>
                        <GradientStop Color="#E9ECEF" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="12" Opacity="0.15" Color="#000000"/>
                </Border.Effect>
        
        <Grid Margin="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
                    <Border Grid.Row="0" Background="#1976D2" CornerRadius="8,8,0,0" Padding="20,16">
                <Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <Ellipse Width="24" Height="24" Margin="0,0,12,0">
                            <Ellipse.Fill>
                                <ImageBrush ImageSource="/Yidev.LocalAI;component/chats.png" Stretch="UniformToFill"/>
                            </Ellipse.Fill>
                        </Ellipse>
                        <TextBlock Text="会话管理" FontSize="16" FontWeight="Bold" Foreground="White"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 会话列表 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" 
                                  Margin="0,8,0,8">
                <ListBox ItemsSource="{Binding Topics}" SelectedItem="{Binding SelectedTopic}"
                                 Background="Transparent" BorderThickness="0" 
                                 ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                 ItemContainerStyle="{StaticResource ModernListBoxItemStyle}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*" MinWidth="40"/>
                                    <ColumnDefinition Width="60"/>
                                </Grid.ColumnDefinitions>

                                <Ellipse Grid.Column="0" Width="12" Height="12" Margin="0,0,8,0">
                                    <Ellipse.Fill>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#4A90E2" Offset="0"/>
                                            <GradientStop Color="#357ABD" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Ellipse.Fill>
                                </Ellipse>

                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                    <TextBlock Text="{Binding Name}"
                                                               FontSize="14" FontWeight="Medium"
                                                               TextTrimming="CharacterEllipsis"
                                                               TextWrapping="NoWrap"/>
                                                    <TextBlock Text="{Binding Path=CreatedAt, Converter={StaticResource DateTimeToRelativeTimeConverter}}"
                                                               FontSize="10"
                                                               Opacity="0.6"
                                                               TextTrimming="CharacterEllipsis"
                                                               TextWrapping="NoWrap"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>

                                <Border Grid.Column="2" HorizontalAlignment="Right" Background="Transparent">
                                    <StackPanel Orientation="Horizontal" Visibility="{Binding IsMouseOver, RelativeSource={RelativeSource AncestorType=ListBoxItem}, Converter={StaticResource BoolToVisibilityConverter2}}">
                                    <!-- 编辑按钮 -->
                                    <Button Command="{Binding DataContext.EditTopicCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                            CommandParameter="{Binding}"
                                            Background="#4CAF50" 
                                            Width="24" Height="24" 
                                            BorderThickness="0"
                                            Cursor="Hand"
                                            ToolTip="编辑话题名称"
                                            Margin="0,0,4,0">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border x:Name="border" 
                                                                    CornerRadius="12" 
                                                                    Background="{TemplateBinding Background}"
                                                                    Width="24" Height="24">
                                                                <Path Data="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                                                                      Fill="White" 
                                                                      Stretch="Uniform" 
                                                                      Width="12" Height="12"
                                                                      HorizontalAlignment="Center" 
                                                                      VerticalAlignment="Center"/>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#45A049" TargetName="border"/>
                                                                    <Setter Property="LayoutTransform">
                                                                        <Setter.Value>
                                                                            <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed" Value="True">
                                                                    <Setter Property="Background" Value="#3E8E41" TargetName="border"/>
                                                                    <Setter Property="LayoutTransform">
                                                                        <Setter.Value>
                                                                            <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                    
                                    <!-- 删除按钮 -->
                                    <Button Command="{Binding DataContext.DeleteTopicCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                            CommandParameter="{Binding}"
                                            Background="#FF6B6B" 
                                            Width="24" Height="24" 
                                            BorderThickness="0"
                                            Cursor="Hand"
                                            ToolTip="删除会话">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border x:Name="border" 
                                                                    CornerRadius="12" 
                                                                    Background="{TemplateBinding Background}"
                                                                    Width="24" Height="24">
                                                                <Path Data="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                                                                      Fill="White" 
                                                                      Stretch="Uniform" 
                                                                      Width="12" Height="12"
                                                                      HorizontalAlignment="Center" 
                                                                      VerticalAlignment="Center"/>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#FF5252" TargetName="border"/>
                                                                    <Setter Property="LayoutTransform">
                                                                        <Setter.Value>
                                                                            <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed" Value="True">
                                                                    <Setter Property="Background" Value="#E53935" TargetName="border"/>
                                                                    <Setter Property="LayoutTransform">
                                                                        <Setter.Value>
                                                                            <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </StackPanel>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </ScrollViewer>

            <!-- 新建话题按钮 -->
            <Border Grid.Row="2" Margin="16,8,16,16">
                <Button Content="+ 新的对话" Command="{Binding NewTopicCommand}" 
                                Style="{StaticResource ModernButtonStyle}"
                                HorizontalAlignment="Stretch"/>
            </Border>
        </Grid>
    </Border>

    <!-- 纯GridSplitter实现 -->
    <GridSplitter Grid.Column="1" 
                  HorizontalAlignment="Stretch" 
                  VerticalAlignment="Stretch"
                  Background="#E9ECEF"
                  Cursor="SizeWE"
                  BorderThickness="0"
                  IsTabStop="False"
                  ResizeDirection="Columns"
                  ResizeBehavior="PreviousAndNext">
        <GridSplitter.Template>
            <ControlTemplate TargetType="GridSplitter">
                <Border Background="{TemplateBinding Background}">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <Ellipse Width="3" Height="3" Fill="#667eea" Margin="0,2"/>
                        <Ellipse Width="3" Height="3" Fill="#667eea" Margin="0,2"/>
                        <Ellipse Width="3" Height="3" Fill="#667eea" Margin="0,2"/>
                    </StackPanel>
                </Border>
            </ControlTemplate>
        </GridSplitter.Template>
    </GridSplitter>
    
    <!-- 收缩/展开按钮列 -->
    <Button x:Name="ToggleButton" 
            Grid.Column="2"
            Style="{StaticResource IconButtonStyle}"
            VerticalAlignment="Center"
            HorizontalAlignment="Center"
            Width="20" Height="20">
        <Path x:Name="ToggleButtonIcon" 
              Data="M 6 2 L 2 6 L 6 10" 
              Stroke="#667eea" 
              StrokeThickness="2.5" 
              StrokeLineJoin="Round"/>
    </Button>

    <!-- 右侧聊天区域 -->
    <Grid Grid.Column="3" Margin="0,0,8,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#1976D2" CornerRadius="8,8,0,0" Padding="20,15">
                <Grid>
                    <StackPanel Orientation="Horizontal">
                        <Image Source="/Yidev.LocalAI;component/Yidev.png" Width="48" Margin="0,-10,12,-10" VerticalAlignment="Center"/>
                        <TextBlock Text="本地AI助手" FontSize="20" Foreground="White" FontWeight="Bold" 
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Text="基于本地模型提供服务" Foreground="#E1F5FE" FontSize="12" 
                               HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,40,0"/>

                    <!-- 设置按钮 -->
                    <Button x:Name="SettingsButton" Style="{StaticResource IconButtonStyle}" Width="24" Height="24"
                            HorizontalAlignment="Right" VerticalAlignment="Center" ToolTip="设置">
                        <Path Data="M12 8c-2.2 0-4 1.8-4 4s1.8 4 4 4 4-1.8 4-4-1.8-4-4-4zm8.5 4c0-.9-.1-1.8-.3-2.6l2.8-2.2-2.8-4.8-3.3 1.3c-.7-.5-1.5-.9-2.4-1.2L13 0h-4l-.5 3.5c-.9.3-1.7.7-2.4 1.2L2.8 3.4 0 8l2.8 2.2c-.2.8-.3 1.7-.3 2.6s.1 1.8.3 2.6L0 17.6l2.8 4.8 3.3-1.3c.7.5 1.5.9 2.4 1.2L9 24h4l.5-3.5c.9-.3 1.7-.7 2.4-1.2l3.3 1.3 2.8-4.8-2.8-2.2c.2-.8.3-1.7.3-2.6z" Fill="White" Stretch="Uniform" />
                    </Button>
                </Grid>
            </Border>

            <!-- 聊天记录区域 -->
            <Border Grid.Row="1" Margin="15,15,15,5" Background="#F5F5F5" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1"/>
                </Border.Effect>

                <ScrollViewer x:Name="ChatScrollViewer" VerticalScrollBarVisibility="Auto" Padding="5">
                    <StackPanel>
                        <ItemsControl ItemsSource="{Binding Messages}" Background="Transparent" BorderThickness="0">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="5,8">
                                    <!-- AI消息布局：头像 - [消息气泡 + 操作按钮] -->
                                    <Grid Visibility="{Binding Role, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- AI头像 (左侧) -->
                                        <Ellipse Width="40" Height="40" Grid.Column="0" Margin="0,0,10,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.2"/>
                                            </Ellipse.Effect>
                                            <Ellipse.Fill>
                                                <ImageBrush ImageSource="{Binding Avatar}" Stretch="UniformToFill"/>
                                            </Ellipse.Fill>
                                        </Ellipse>

                                        <!-- AI消息气泡和操作按钮容器 -->
                                        <Border x:Name="AIMessageContainer" Grid.Column="1" HorizontalAlignment="Left" Background="Transparent" Padding="0,0,10,0">
                                            <StackPanel Orientation="Horizontal">
                                                <!-- AI消息气泡 -->
                                                <Border CornerRadius="18" Padding="15,10" MaxWidth="500">
                                                    <Border.Effect>
                                                        <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.2"/>
                                                    </Border.Effect>
                                                    <Border.Background>
                                                        <SolidColorBrush Color="{Binding Role, Converter={StaticResource BoolToColorConverter}}"/>
                                                    </Border.Background>

                                                    <StackPanel>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                        
                                                            <!-- AI消息：用户名在左，日期在右 -->
                                                            <TextBlock Grid.Column="0" Text="{Binding SenderName}"
                                                                       Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}" 
                                                                       FontWeight="Bold" Margin="0,0,0,5"/>
                                                                   
                                                            <!-- AI消息：日期在右 -->
                                                            <TextBlock Grid.Column="1" Text="{Binding Path=Timestamp, Converter={StaticResource DateTimeToRelativeTimeConverter}}" 
                                                                       FontSize="10" HorizontalAlignment="Right" Opacity="0.7"
                                                                       Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}"/>
                                                        </Grid>
                                                        <!-- AI消息使用Markdown渲染 -->
                                                        <markdig:MarkdownViewer Markdown="{Binding Content}"
                                                                               Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}"
                                                                               FontSize="14"
                                                                               Margin="0,2,0,2"
                                                                               Background="Transparent"
                                                                               BorderThickness="0"/>

                                                        <!-- AI消息图片显示区域 -->
                                                        <ItemsControl ItemsSource="{Binding ImageUrls}"
                                                                     Visibility="{Binding HasImages, Converter={StaticResource BoolToVisibilityConverter2}}"
                                                                     Margin="0,8,0,0">
                                                            <ItemsControl.ItemsPanel>
                                                                <ItemsPanelTemplate>
                                                                    <WrapPanel Orientation="Horizontal" MaxWidth="450"/>
                                                                </ItemsPanelTemplate>
                                                            </ItemsControl.ItemsPanel>
                                                            <ItemsControl.ItemTemplate>
                                                                <DataTemplate>
                                                                    <Border CornerRadius="8" Margin="4"
                                                                            Background="White"
                                                                            BorderBrush="#E0E0E0"
                                                                            BorderThickness="1"
                                                                            MaxWidth="200"
                                                                            MaxHeight="200">
                                                                        <Border.Effect>
                                                                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                                                                        </Border.Effect>
                                                                        <Grid>
                                                                            <!-- 图片 -->
                                                                            <Image Source="{Binding}"
                                                                                   Stretch="UniformToFill"
                                                                                   StretchDirection="DownOnly"
                                                                                   MaxWidth="190"
                                                                                   MaxHeight="190"
                                                                                   Margin="5"
                                                                                   Cursor="Hand"
                                                                                   ToolTip="点击查看大图">
                                                                                <Image.Style>
                                                                                    <Style TargetType="Image">
                                                                                        <Setter Property="RenderTransform">
                                                                                            <Setter.Value>
                                                                                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                                                                                            </Setter.Value>
                                                                                        </Setter>
                                                                                        <Style.Triggers>
                                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                                <Setter Property="RenderTransform">
                                                                                                    <Setter.Value>
                                                                                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                                                                    </Setter.Value>
                                                                                                </Setter>
                                                                                            </Trigger>
                                                                                        </Style.Triggers>
                                                                                    </Style>
                                                                                </Image.Style>
                                                                            </Image>

                                                                            <!-- 加载失败时的占位符 -->
                                                                            <Border Background="#F5F5F5"
                                                                                    CornerRadius="4"
                                                                                    Visibility="Collapsed"
                                                                                    x:Name="ImageErrorPlaceholder">
                                                                                <StackPanel HorizontalAlignment="Center"
                                                                                           VerticalAlignment="Center"
                                                                                           Margin="20">
                                                                                    <Path Data="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"
                                                                                          Fill="#BDBDBD"
                                                                                          Width="24"
                                                                                          Height="24"
                                                                                          Stretch="Uniform"/>
                                                                                    <TextBlock Text="图片加载失败"
                                                                                              FontSize="10"
                                                                                              Foreground="#757575"
                                                                                              Margin="0,4,0,0"
                                                                                              HorizontalAlignment="Center"/>
                                                                                </StackPanel>
                                                                            </Border>
                                                                        </Grid>
                                                                    </Border>
                                                                </DataTemplate>
                                                            </ItemsControl.ItemTemplate>
                                                        </ItemsControl>
                                                    </StackPanel>
                                                </Border>

                                                <!-- AI消息操作按钮 (紧挨着气泡) -->
                                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="8,0,0,0"
                                                            Visibility="{Binding IsMouseOver, ElementName=AIMessageContainer, Converter={StaticResource BoolToVisibilityConverter2}}">
                                            <!-- 复制按钮 -->
                                            <Button Command="{Binding DataContext.CopyCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Style="{StaticResource ChatActionButtonStyle}" 
                                                    ToolTip="复制">
                                                <Path Data="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
                                                      Fill="White" 
                                                      Stretch="Uniform" 
                                                      Width="16" Height="16"/>
                                            </Button>
                                            
                                            <!-- 删除按钮 -->
                                            <Button Command="{Binding DataContext.DeleteMessageCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Background="#FF6B6B"
                                                    BorderThickness="0"
                                                    Width="32" Height="32"
                                                    Margin="2"
                                                    Cursor="Hand"
                                                    ToolTip="删除">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border x:Name="border" 
                                                                            CornerRadius="16" 
                                                                            Background="{TemplateBinding Background}"
                                                                            Width="32" Height="32">
                                                                        <Path Data="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                                                                              Fill="White" 
                                                                              Stretch="Uniform" 
                                                                              Width="16" Height="16"
                                                                              HorizontalAlignment="Center" 
                                                                              VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#FF5252" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#E53935" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                            
                                            <!-- 重新生成按钮 -->
                                            <Button Command="{Binding DataContext.RegenerateCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Background="#4CAF50"
                                                    BorderThickness="0"
                                                    Width="32" Height="32"
                                                    Margin="2"
                                                    Cursor="Hand"
                                                    ToolTip="重新生成">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border x:Name="border" 
                                                                            CornerRadius="16" 
                                                                            Background="{TemplateBinding Background}"
                                                                            Width="32" Height="32">
                                                                        <Path Data="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
                                                                              Fill="White" 
                                                                              Stretch="Uniform" 
                                                                              Width="16" Height="16"
                                                                              HorizontalAlignment="Center" 
                                                                              VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#45A049" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#3E8E41" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <!-- 用户消息布局：[操作按钮 + 消息气泡] - 头像 -->
                                    <Grid Visibility="{Binding Role, Converter={StaticResource BoolToVisibilityConverter}}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 用户消息气泡和操作按钮容器 -->
                                        <Border x:Name="UserMessageContainer" Grid.Column="0" HorizontalAlignment="Right" Background="Transparent" Padding="10,0,0,0">
                                            <StackPanel Orientation="Horizontal">
                                                <!-- 用户消息操作按钮 (紧挨着气泡) -->
                                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,8,0"
                                                            Visibility="{Binding IsMouseOver, ElementName=UserMessageContainer, Converter={StaticResource BoolToVisibilityConverter2}}">
                                            <!-- 复制按钮 -->
                                            <Button Command="{Binding DataContext.CopyCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Style="{StaticResource ChatActionButtonStyle}" 
                                                    ToolTip="复制">
                                                <Path Data="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
                                                      Fill="White" 
                                                      Stretch="Uniform" 
                                                      Width="16" Height="16"/>
                                            </Button>
                                            
                                            <!-- 编辑按钮 -->
                                            <Button Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Style="{StaticResource ChatActionButtonStyle}" 
                                                    ToolTip="编辑">
                                                <Path Data="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                                                      Fill="White" 
                                                      Stretch="Uniform" 
                                                      Width="16" Height="16"/>
                                            </Button>
                                            
                                            <!-- 删除按钮 -->
                                            <Button Command="{Binding DataContext.DeleteMessageCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                                    CommandParameter="{Binding}" 
                                                    Background="#FF6B6B"
                                                    BorderThickness="0"
                                                    Width="32" Height="32"
                                                    Margin="2"
                                                    Cursor="Hand"
                                                    ToolTip="删除">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border x:Name="border" 
                                                                            CornerRadius="16" 
                                                                            Background="{TemplateBinding Background}"
                                                                            Width="32" Height="32">
                                                                        <Path Data="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                                                                              Fill="White" 
                                                                              Stretch="Uniform" 
                                                                              Width="16" Height="16"
                                                                              HorizontalAlignment="Center" 
                                                                              VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#FF5252" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#E53935" TargetName="border"/>
                                                                            <Setter Property="LayoutTransform">
                                                                                <Setter.Value>
                                                                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                                </StackPanel>

                                                <!-- 用户消息气泡 -->
                                                <Border CornerRadius="18" Padding="15,10" MaxWidth="500">
                                                    <Border.Effect>
                                                        <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.2"/>
                                                    </Border.Effect>
                                                    <Border.Background>
                                                        <SolidColorBrush Color="{Binding Role, Converter={StaticResource BoolToColorConverter}}"/>
                                                    </Border.Background>

                                                    <StackPanel>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                        
                                                            <!-- 用户消息：日期在左 -->
                                                            <TextBlock Grid.Column="0" Text="{Binding Path=Timestamp, Converter={StaticResource DateTimeToRelativeTimeConverter}}"
                                                                       Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}" 
                                                                       FontSize="10" FontWeight="Normal" Opacity="0.7" Margin="0,0,0,5"/>
                                                                   
                                                            <!-- 用户消息：用户名在右 -->
                                                            <TextBlock Grid.Column="1" Text="{Binding SenderName}"
                                                                       Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}" 
                                                                       FontWeight="Bold" HorizontalAlignment="Right" Margin="0,0,0,5"/>
                                                        </Grid>
                                                        <TextBlock Text="{Binding Content}"
                                                                   Foreground="{Binding Role, Converter={StaticResource BoolToTextColorConverter}}"
                                                                   TextWrapping="Wrap" Style="{StaticResource ChatBubbleText}"/>

                                                        <!-- 用户消息图片显示区域 -->
                                                        <ItemsControl ItemsSource="{Binding ImageUrls}"
                                                                     Visibility="{Binding HasImages, Converter={StaticResource BoolToVisibilityConverter2}}"
                                                                     Margin="0,8,0,0">
                                                            <ItemsControl.ItemsPanel>
                                                                <ItemsPanelTemplate>
                                                                    <WrapPanel Orientation="Horizontal" MaxWidth="450"/>
                                                                </ItemsPanelTemplate>
                                                            </ItemsControl.ItemsPanel>
                                                            <ItemsControl.ItemTemplate>
                                                                <DataTemplate>
                                                                    <Border CornerRadius="8" Margin="4"
                                                                            Background="White"
                                                                            BorderBrush="#E0E0E0"
                                                                            BorderThickness="1"
                                                                            MaxWidth="200"
                                                                            MaxHeight="200">
                                                                        <Border.Effect>
                                                                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                                                                        </Border.Effect>
                                                                        <Grid>
                                                                            <!-- 图片 -->
                                                                            <Image Source="{Binding}"
                                                                                   Stretch="UniformToFill"
                                                                                   StretchDirection="DownOnly"
                                                                                   MaxWidth="190"
                                                                                   MaxHeight="190"
                                                                                   Margin="5"
                                                                                   Cursor="Hand"
                                                                                   ToolTip="点击查看大图">
                                                                                <Image.Style>
                                                                                    <Style TargetType="Image">
                                                                                        <Setter Property="RenderTransform">
                                                                                            <Setter.Value>
                                                                                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                                                                                            </Setter.Value>
                                                                                        </Setter>
                                                                                        <Style.Triggers>
                                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                                <Setter Property="RenderTransform">
                                                                                                    <Setter.Value>
                                                                                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                                                                    </Setter.Value>
                                                                                                </Setter>
                                                                                            </Trigger>
                                                                                        </Style.Triggers>
                                                                                    </Style>
                                                                                </Image.Style>
                                                                            </Image>

                                                                            <!-- 加载失败时的占位符 -->
                                                                            <Border Background="#F5F5F5"
                                                                                    CornerRadius="4"
                                                                                    Visibility="Collapsed"
                                                                                    x:Name="ImageErrorPlaceholder">
                                                                                <StackPanel HorizontalAlignment="Center"
                                                                                           VerticalAlignment="Center"
                                                                                           Margin="20">
                                                                                    <Path Data="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"
                                                                                          Fill="#BDBDBD"
                                                                                          Width="24"
                                                                                          Height="24"
                                                                                          Stretch="Uniform"/>
                                                                                    <TextBlock Text="图片加载失败"
                                                                                              FontSize="10"
                                                                                              Foreground="#757575"
                                                                                              Margin="0,4,0,0"
                                                                                              HorizontalAlignment="Center"/>
                                                                                </StackPanel>
                                                                            </Border>
                                                                        </Grid>
                                                                    </Border>
                                                                </DataTemplate>
                                                            </ItemsControl.ItemTemplate>
                                                        </ItemsControl>
                                                    </StackPanel>
                                                </Border>
                                            </StackPanel>
                                        </Border>

                                        <!-- 用户头像 (右侧) -->
                                        <Ellipse Width="40" Height="40" Grid.Column="1" Margin="5,0,0,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.2"/>
                                            </Ellipse.Effect>
                                            <Ellipse.Fill>
                                                <ImageBrush ImageSource="{Binding Avatar}" Stretch="UniformToFill"/>
                                            </Ellipse.Fill>
                                        </Ellipse>
                                    </Grid>
                                </Grid>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                        
                        <!-- 加载指示器 -->
                        <Grid Margin="5,8"
                              Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter2}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- AI头像 (左侧) -->
                            <Ellipse Width="40" Height="40" Grid.Column="0" Margin="0,0,10,0">
                                <Ellipse.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.2"/>
                                </Ellipse.Effect>
                                <Ellipse.Fill>
                                    <ImageBrush ImageSource="pack://application:,,,/uni-ai.png" Stretch="UniformToFill"/>
                                </Ellipse.Fill>
                            </Ellipse>

                            <!-- 加载消息气泡 -->
                            <Border Grid.Column="1" CornerRadius="18" Padding="15,10" 
                                    HorizontalAlignment="Left" MaxWidth="500" Background="#26A69A">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.2"/>
                                </Border.Effect>

                                <StackPanel>
                                    <TextBlock Text="AI助手" Foreground="White" FontWeight="Bold" Margin="0,0,0,5"/>
                                    
                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                        <!-- 旋转加载图标 -->
                                        <Ellipse Width="20" Height="20" Margin="0,0,10,0" 
                                                 Fill="White" Opacity="0.8">
                                            <Ellipse.RenderTransform>
                                                <RotateTransform x:Name="LoadingRotation"/>
                                            </Ellipse.RenderTransform>
                                            <Ellipse.Triggers>
                                                <EventTrigger RoutedEvent="Loaded">
                                                    <BeginStoryboard>
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <DoubleAnimation Duration="0:0:1" From="0" To="360"
                                                                             Storyboard.TargetName="LoadingRotation"
                                                                             Storyboard.TargetProperty="Angle"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </Ellipse.Triggers>
                                        </Ellipse>
                                        
                                        <!-- 打字机效果文本 -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="正在思考" Foreground="White" FontSize="14"/>
                                            <TextBlock x:Name="TypingDots" Text="..." Foreground="White" FontSize="14" Margin="2,0,0,0">
                                                <TextBlock.Triggers>
                                                    <EventTrigger RoutedEvent="Loaded">
                                                        <BeginStoryboard>
                                                            <Storyboard RepeatBehavior="Forever">
                                                                <DoubleAnimation Duration="0:0:0.8" From="0" To="1" AutoReverse="True"
                                                                                 Storyboard.TargetProperty="Opacity"/>
                                                            </Storyboard>
                                                        </BeginStoryboard>
                                                    </EventTrigger>
                                                </TextBlock.Triggers>
                                            </TextBlock>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 输入区域 -->
            <Border Grid.Row="2" Background="#FFFFFF" CornerRadius="8" Margin="15,5,15,15" Padding="10">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.2"/>
                </Border.Effect>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" MinHeight="50" MaxHeight="120" Padding="15,12" 
             VerticalContentAlignment="Center" FontSize="15" Background="#F8F9FA"
             BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,10,0"
             AcceptsReturn="True" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
             Text="{Binding UserInput, UpdateSourceTrigger=PropertyChanged}">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SendMessageCommand}"/>
                        </TextBox.InputBindings>
                        <!-- TextBox样式 -->
                        <TextBox.Resources>
                            <Style TargetType="TextBox">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                            <Border x:Name="border" CornerRadius="20" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                                <ScrollViewer x:Name="PART_ContentHost" Padding="{TemplateBinding Padding}" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="BorderBrush" Value="#BBDEFB" TargetName="border"/>
                                                </Trigger>
                                                <Trigger Property="IsFocused" Value="True">
                                                    <Setter Property="BorderBrush" Value="#2196F3" TargetName="border"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Resources>
                    </TextBox>

                    <Button Grid.Column="1" Width="50" Height="50" 
                Background="#2196F3" Foreground="White" 
                Style="{StaticResource RoundButtonStyle}"
                Command="{Binding SendMessageCommand}">
                        <Grid>
                            <!-- 正常发送文本 -->
                            <TextBlock Text="发送" FontSize="14" FontWeight="Bold"
                                       Visibility="{Binding IsBusy, Converter={StaticResource InverseBoolToVisibilityConverter2}}"/>
                            
                            <!-- 加载状态 -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center"
                                        Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibilityConverter2}}">
                                <Ellipse Width="16" Height="16" Fill="White" Opacity="0.9">
                                    <Ellipse.RenderTransform>
                                        <RotateTransform x:Name="SendButtonRotation"/>
                                    </Ellipse.RenderTransform>
                                    <Ellipse.Triggers>
                                        <EventTrigger RoutedEvent="Loaded">
                                            <BeginStoryboard>
                                                <Storyboard RepeatBehavior="Forever">
                                                    <DoubleAnimation Duration="0:0:1" From="0" To="360"
                                                                     Storyboard.TargetName="SendButtonRotation"
                                                                     Storyboard.TargetProperty="Angle"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Ellipse.Triggers>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Button>
                </Grid>
            </Border>
        </Grid>
        </Grid>
    </Border>
</Window>
