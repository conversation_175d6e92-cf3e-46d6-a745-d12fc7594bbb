﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using ModelContextProtocol.SemanticKernel;
using System.Threading.Tasks;
using Yidev.LocalAI.Models;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using ModelContextProtocol.SemanticKernel.Extensions;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.IO;
using System.Text;
using Serilog;
using Yidev.LocalAI.McpPlugins;
using Windows.System;
using System.Runtime.InteropServices;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Yidev.LocalAI.Services
{
    public class SemanticKernelService
    {
        private readonly Kernel _kernel;
        private readonly IChatCompletionService _chatCompletionService;
        private readonly ChatHistory _chatHistory;
        private readonly string _baseUploadDirectory;
        private bool _customPluginsAdded = false;

        private SemanticKernelService()
        {
            Log.Information("Initializing SemanticKernelService...");

            var builder = Kernel.CreateBuilder();
            //builder.Services.AddLogging(c => c.AddDebug().SetMinimumLevel(LogLevel.Trace));

            // 使用本地的与OpenAI兼容的终结点进行配置
            builder.AddOpenAIChatCompletion(
                modelId: "gemini-2.5-pro-preview-06-05",
                apiKey: "sk-5PPE23EcS2eMpcms8e73Df0180Ac4332871aF412Ea90302f",
                endpoint: new System.Uri("https://api.cxhao.com/v1")); // 本地AI服务器的标准终结点
            /*
            builder.AddOpenAIChatCompletion(
                modelId: "gemini-2.5-pro-preview-06-05",
                apiKey: "", //目前 higress 的AI路由管理对认证方式仅支持 Key Auth 认证，而消费者认证暂不支持OAuth2和JWT
                endpoint: new System.Uri("https://ai.yidev.cn/openai/v1")); // 本地AI服务器的标准终结点
            */
            _kernel = builder.Build();

            _chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();
            _chatHistory = new ChatHistory();
            _baseUploadDirectory = Path.Combine(AppContext.BaseDirectory, "uploads");
            Log.Information("SemanticKernelService initialized.");
        }

        private async Task InitializeAsync()
        {
            try
            {
                // 添加 MCP Filesystem 插件
                var fsPlugin = await _kernel.Plugins.AddMcpFunctionsFromStdioServerAsync("filesystem", "npx", ["-y", "@modelcontextprotocol/server-filesystem", _baseUploadDirectory]);
                Log.Information($"MCP filesystem 插件已添加: {fsPlugin.Name}");

                var dbPlugin = await _kernel.Plugins.AddMcpFunctionsFromStdioServerAsync("database-server", "npx", ["-y", "@executeautomation/database-server", "uploads/llk/temp.db"]);
                Log.Information($"MCP database-server 插件已添加: {dbPlugin.Name}");

                var playwrightPlugin = await _kernel.Plugins.AddMcpFunctionsFromSseServerAsync("playwright", new Uri("http://127.0.0.1:8931/sse"));
                Log.Information($"MCP playwright 插件已添加: {playwrightPlugin.Name}");

                // 延迟添加自定义插件，在第一次使用时添加
                // await AddCustomPluginsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "在 Kernel 工厂方法中添加共享 MCP 插件时出错");
                // 抛出异常会阻止 Kernel 创建，这通常是期望的行为
                throw new InvalidOperationException("无法初始化共享 MCP 插件。", ex);
            }
        }

        private void EnsureCustomPluginsAdded()
        {
            if (_customPluginsAdded) return;

            try
            {
                // 直接创建插件实例，而不是从 DI 容器获取
                // 这样可以避免循环依赖问题

                // 创建 FileReaderPlugin 实例
                var loggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder =>
                    builder.AddConsole().AddDebug());
                var fileReaderLogger = loggerFactory.CreateLogger<FileReaderPlugin>();
                var fileReaderPlugin = new FileReaderPlugin(fileReaderLogger);

                _kernel.Plugins.AddFromObject(fileReaderPlugin, nameof(FileReaderPlugin));
                Log.Information("FileReaderPlugin 已添加到 Kernel");

                // 创建 FileWriterPlugin 实例
                var fileWriterLogger = loggerFactory.CreateLogger<FileWriterPlugin>();
                var fileWriterPlugin = new FileWriterPlugin(fileWriterLogger);

                _kernel.Plugins.AddFromObject(fileWriterPlugin, nameof(FileWriterPlugin));
                Log.Information("FileWriterPlugin 已添加到 Kernel");

                _customPluginsAdded = true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "添加自定义插件时出错");
                // 不抛出异常，允许服务继续运行，只是没有自定义插件
            }
        }

        public static async Task<SemanticKernelService> CreateAsync()
        {
            var service = new SemanticKernelService();
            await service.InitializeAsync();
            return service;
        }

        // 获取当前用户的上传目录
        private string GetUserUploadDirectory()
        {
            var viewModel = ViewModelLocator.MainViewModelInstance; // Use shared instance
            var userDirectory = Path.Combine(_baseUploadDirectory, viewModel.Username);

            // 确保用户目录存在
            if (!Directory.Exists(userDirectory))
            {
                Directory.CreateDirectory(userDirectory);
                // 根据操作系统设置权限
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) || RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    try
                    {
                        var chmod = new ProcessStartInfo
                        {
                            FileName = "chmod",
                            Arguments = $"777 '{userDirectory}'",
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        };
                        using (var process = Process.Start(chmod))
                        {
                            process.WaitForExit();
                            // 可选：可以读取输出和错误日志
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, $"设置目录权限时出错: {userDirectory}");
                    }
                }
            }

            // 统一路径风格为 /，避免AI插件不识别
            var unixStyleDirectory = userDirectory.Replace("\\", "/");
            return unixStyleDirectory;
        }

        public void SetConversationHistory(IEnumerable<ChatMessage> messages)
        {
            // 添加系统消息，设置工作目录
            var userDirectory = GetUserUploadDirectory();
            Log.Information("User directory: " + userDirectory);

            Log.Information("Setting conversation history. Message count: {MessageCount}", messages.Count());
            _chatHistory.Clear();
            // 强化提示词，明确路径要求，并强制使用文件读取工具
            // 使用 nameof(FileReaderPlugin) 获取插件名称，确保与注册时一致
            var fileReaderPluginName = nameof(FileReaderPlugin);
            var prompt = $@"
                **重要指令：**
                0. 你的工作根目录是：'{userDirectory}'，不要再询问路径。所有文件路径都必须基于此目录。
                1. 如果用户未指定文件路径，默认操作你的根目录（即：'{userDirectory}'）。
                2.  所有文件操作（读取、写入、工具调用参数）**必须**使用基于此根目录的**完整绝对路径**。例如：'{userDirectory}/示例文件.xlsx'。
                3.  **严禁**在文件操作或工具调用中使用相对路径或仅文件名。
                4.  **当需要获取或确认任何文件的具体内容时，你必须调用 `{fileReaderPluginName}.ReadFileContentAsync` 工具，并提供文件的完整绝对路径。**
                5.  **严禁在未成功调用 `{fileReaderPluginName}.ReadFileContentAsync` 工具获取信息的情况下，猜测、编造或总结文件内容。**
                6.  如果 `{fileReaderPluginName}.ReadFileContentAsync` 工具返回错误（例如，文件未找到、无法读取、权限不足、文件过大），请直接将工具返回的错误信息告知用户，不要自行解释或猜测。
                7.  如果 `{fileReaderPluginName}.ReadFileContentAsync` 工具返回了文件内容，请基于这些返回的实际内容进行回答。
                ";
            _chatHistory.AddSystemMessage(prompt);

            foreach (var message in messages)
            {
                if (message.Role == "user")
                {
                    _chatHistory.AddUserMessage(message.Content);
                }
                else if (message.Role == "AI助手" || message.Role == "assistant")
                {
                    _chatHistory.AddAssistantMessage(message.Content);
                }
            }
        }

        public async Task<string> GetChatResponseAsync(string userMessage)
        {
            Log.Information("Getting chat response for user message: {UserMessage}", userMessage);

            // 确保自定义插件已添加
            EnsureCustomPluginsAdded();

            _chatHistory.AddUserMessage(userMessage);

            var executionSettings = new OpenAIPromptExecutionSettings
            {
                //MaxTokens = 1000000,
                Temperature = 0,
                // 使用 ToolCallBehavior.AutoInvokeKernelFunctions 来让 Semantic Kernel 自动处理函数/工具的调用和执行。
                // 这通常是推荐的方式，前提是 Kernel 实例 (_kernel) 被传递给聊天完成服务。
                ToolCallBehavior = ToolCallBehavior.AutoInvokeKernelFunctions,
                // FunctionChoiceBehavior = FunctionChoiceBehavior.Auto() // 旧版或特定场景下的设置，ToolCallBehavior 优先
            };

            /*
            // 使用 GetStreamingChatMessageContentsAsync 获取更丰富的流式内容，包括函数调用
            IAsyncEnumerable<StreamingChatMessageContent> streamingResults = _chatCompletionService.GetStreamingChatMessageContentsAsync(
                _chatHistory,
                executionSettings,
                _kernel); // 传递 Kernel 以便执行 Function Call
            */
            var result = await _chatCompletionService.GetChatMessageContentAsync(
                _chatHistory,
                executionSettings,
                _kernel); // 传递 Kernel 以便执行 Function Call
            _chatHistory.Add(result); // 将助手的响应添加到历史记录中，以备下一轮使用

            Log.Information("Received chat response: {ResponseContent}", result.Content);
            return result.Content;
        }

        /// <summary>
        /// 根据用户消息生成简洁的话题标题
        /// </summary>
        /// <param name="userMessage">用户的第一条消息</param>
        /// <returns>生成的话题标题</returns>
        public async Task<string> GenerateTopicTitleAsync(string userMessage)
        {
            Log.Information("Generating topic title for user message: {UserMessage}", userMessage);

            var tempChatHistory = new ChatHistory();
            tempChatHistory.AddSystemMessage("你是一个专门生成对话标题的助手。请根据用户的问题或消息，生成一个简洁、准确的标题，不超过15个字符。只返回标题内容，不要包含任何其他文字或标点符号。");
            tempChatHistory.AddUserMessage($"请为以下消息生成一个简洁的标题：{userMessage}");

            var executionSettings = new OpenAIPromptExecutionSettings
            {
                MaxTokens = 50,
                Temperature = 0.3,
            };

            try
            {
                var result = await _chatCompletionService.GetChatMessageContentAsync(
                    tempChatHistory,
                    executionSettings,
                    _kernel);

                var title = result.Content?.Trim();

                // 如果生成的标题为空或过长，使用默认格式
                if (string.IsNullOrWhiteSpace(title) || title.Length > 20)
                {
                    title = userMessage.Length > 15 ? userMessage.Substring(0, 15) + "..." : userMessage;
                }

                Log.Information("Generated topic title: {Title}", title);
                return title;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error generating topic title");
                // 如果生成失败，使用用户消息的前15个字符作为标题
                var fallbackTitle = userMessage.Length > 15 ? userMessage.Substring(0, 15) + "..." : userMessage;
                return fallbackTitle;
            }
        }
    }
}
