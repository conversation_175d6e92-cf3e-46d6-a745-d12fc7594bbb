﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Yidev.LocalAI.Tests
{
    public class TestImageExtraction
    {
        public static void Main2()
        {
            Console.WriteLine("=== 图片URL提取测试 ===");
            
            var testCases = new[]
            {
                // Markdown格式测试
                "![示例图片](https://picsum.photos/300/200?random=1)",
                "这是一张图片：![alt](https://example.com/image.jpg)",
                
                // HTML格式测试
                "<img src=\"https://picsum.photos/350/250?random=3\" alt=\"HTML图片\" />",
                "<img src='https://example.com/test.png' />",
                
                // 直接URL测试
                "https://picsum.photos/300/300?random=6",
                "请看这个链接：https://example.com/photo.jpeg",
                
                // 混合格式测试
                @"这里有多种格式的图片：

Markdown格式：
![图片1](https://picsum.photos/200/150?random=9)

HTML格式：
<img src=""https://picsum.photos/250/180?random=10"" alt=""图片2"" />

直接URL：
https://picsum.photos/300/200?random=11

文本和图片混合显示效果很好！"
            };

            for (int i = 0; i < testCases.Length; i++)
            {
                Console.WriteLine($"\n--- 测试用例 {i + 1} ---");
                Console.WriteLine($"输入: {testCases[i]}");
                
                var urls = ExtractImageUrls(testCases[i]);
                Console.WriteLine($"提取到的URL数量: {urls.Count}");
                
                foreach (var url in urls)
                {
                    Console.WriteLine($"  ✓ {url}");
                }
                
                if (urls.Count == 0)
                {
                    Console.WriteLine("  ❌ 没有提取到任何图片URL");
                }
            }
            
            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        private static List<string> ExtractImageUrls(string content)
        {
            var imageUrls = new List<string>();

            if (string.IsNullOrWhiteSpace(content))
                return imageUrls;

            try
            {
                // 1. Markdown图片语法: ![alt](url)
                var markdownPattern = @"!\[.*?\]\((https?://[^\s\)]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s\)]*)?)\)";
                var markdownMatches = Regex.Matches(content, markdownPattern,
                    RegexOptions.IgnoreCase | RegexOptions.Multiline);

                Console.WriteLine($"  Markdown匹配数: {markdownMatches.Count}");
                foreach (Match match in markdownMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                        Console.WriteLine($"    Markdown: {url}");
                    }
                }

                // 2. HTML img标签: <img src="url" />
                var htmlPattern = @"<img[^>]+src=[""']?(https?://[^\s""'>]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s""'>]*)?)[""']?[^>]*>";
                var htmlMatches = Regex.Matches(content, htmlPattern,
                    RegexOptions.IgnoreCase | RegexOptions.Multiline);

                Console.WriteLine($"  HTML匹配数: {htmlMatches.Count}");
                foreach (Match match in htmlMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                        Console.WriteLine($"    HTML: {url}");
                    }
                }

                // 3. 直接的图片URL (独立一行或被空格包围)
                var directUrlPattern = @"(?:^|\s)(https?://[^\s]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s]*)?)(?:\s|$)";
                var directMatches = Regex.Matches(content, directUrlPattern,
                    RegexOptions.IgnoreCase | RegexOptions.Multiline);

                Console.WriteLine($"  直接URL匹配数: {directMatches.Count}");
                foreach (Match match in directMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                        Console.WriteLine($"    直接URL: {url}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }

            return imageUrls;
        }
    }
}
