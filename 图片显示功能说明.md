# AI助手消息图片显示功能说明

## 功能概述

本次更新为 Yidev.LocalAI 项目的AI助手消息添加了图片显示支持。现在AI助手和用户的消息都可以显示图片内容，支持多种图片格式和URL来源。

## 实现细节

### 1. 数据模型扩展

#### ChatMessage 类新增属性
- **ImageUrls**: `List<string>` - 消息中包含的图片URL列表
- **HasImages**: `bool` - 判断消息是否包含图片的只读属性

```csharp
[NotMapped]
public List<string> ImageUrls { get; set; } = new List<string>();

[NotMapped]
public bool HasImages => ImageUrls?.Any() == true;
```

### 2. UI界面更新

#### MainWindow.xaml 修改
- 为AI消息和用户消息都添加了图片显示区域
- 使用 `ItemsControl` 和 `WrapPanel` 布局多张图片
- 图片支持鼠标悬停放大效果
- 添加了图片加载失败的占位符

#### 图片显示特性
- **最大尺寸**: 200x200 像素
- **布局**: 水平排列，自动换行
- **样式**: 圆角边框，阴影效果
- **交互**: 鼠标悬停放大1.05倍
- **容错**: 加载失败时显示占位符

### 3. 图片URL解析

#### ExtractImageUrls 方法
支持多种图片URL格式的自动识别：

1. **Markdown图片语法**: `![alt](url)`
2. **HTML img标签**: `<img src="url" />`
3. **直接URL**: 独立一行或被空格包围的图片URL

#### 支持的图片格式
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP
- SVG

#### 正则表达式匹配
```csharp
// Markdown图片语法
@"!\[.*?\]\((https?://[^\s\)]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s\)]*)?)\)"

// HTML img标签
@"<img[^>]+src=[""']?(https?://[^\s""'>]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s""'>]*)?)[""']?[^>]*>"

// 直接URL
@"(?:^|\s)(https?://[^\s]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s]*)?)(?:\s|$)"
```

## 使用示例

### AI助手返回包含图片的消息

当AI助手的响应包含以下任一格式时，图片将自动显示：

```markdown
这是一张示例图片：
![示例图片](https://example.com/image.jpg)

或者使用HTML格式：
<img src="https://example.com/image.png" alt="示例图片" />

或者直接提供URL：
https://example.com/image.gif
```

### 用户发送包含图片的消息

用户也可以在消息中包含图片URL，格式与AI助手相同。

## 技术特点

- **自动解析**: 无需手动配置，自动从消息内容中提取图片URL
- **多格式支持**: 支持Markdown、HTML和直接URL三种格式
- **性能优化**: 使用正则表达式高效匹配，避免重复URL
- **用户体验**: 图片与文本内容分离显示，布局美观
- **容错处理**: 图片加载失败时显示友好的占位符

## 注意事项

1. **网络依赖**: 图片显示需要网络连接
2. **HTTPS支持**: 建议使用HTTPS协议的图片URL
3. **性能考虑**: 大量图片可能影响界面响应速度
4. **安全性**: 仅支持HTTP/HTTPS协议的图片URL

## 测试方法

可以通过以下方式测试图片显示功能：

1. 在聊天中发送包含图片URL的消息
2. 让AI助手生成包含图片链接的响应
3. 测试不同格式的图片URL（Markdown、HTML、直接URL）
4. 测试图片加载失败的情况

## 未来扩展

- 支持本地图片文件
- 图片点击放大查看
- 图片下载功能
- 图片缓存机制
- 支持更多图片格式
