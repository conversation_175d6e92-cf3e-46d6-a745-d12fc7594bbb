﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Yidev.LocalAI.Services;
using System.Linq;
using Yidev.Agent.Oidc;
using System.Text.Json.Nodes;
using Serilog;

namespace Yidev.LocalAI.Models
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly TopicService _topicService;
        private SemanticKernelService _semanticKernelService;
        private ObservableCollection<ChatMessage> _messages;
        private ObservableCollection<Topic> _topics;
        private string _userInput = string.Empty;
        private bool _isBusy;
        private Topic _selectedTopic;
        private ICommand _sendMessageCommand;
        private ICommand _newTopicCommand;
        private ICommand _deleteTopicCommand;
        private ICommand _editTopicCommand;
        private string _userInfo;
        private string _avatar;
        private string _username;

        public MainViewModel()
        {
            if (AuthenticationService.CurrentAuthenticationData == null)
            {
                Yidev.Agent.Oidc.MainWindow mainWindow = new Yidev.Agent.Oidc.MainWindow();
                mainWindow.ShowDialog();
            }
            else
            {
                _userInfo = AuthenticationService.CurrentAuthenticationData.UserInfoJson;
                var obj = JsonObject.Parse(_userInfo)?.AsObject();
                _username = obj?.AsObject()?["name"].GetValue<string>() ?? string.Empty;
                _avatar = obj?.AsObject()?["avatar"].GetValue<string>() ?? string.Empty;
            }

            _topicService = new TopicService(new DatabaseContext());
            _messages = new ObservableCollection<ChatMessage>();
            _topics = new ObservableCollection<Topic>();
        }

        public async Task InitializeAsync()
        {
            _semanticKernelService = await SemanticKernelService.CreateAsync();
            await LoadTopics();
        }

        public ObservableCollection<ChatMessage> Messages
        {
            get => _messages;
            set { _messages = value; OnPropertyChanged(); }
        }

        public ObservableCollection<Topic> Topics
        {
            get => _topics;
            set { _topics = value; OnPropertyChanged(); }
        }

        public string UserInput
        {
            get => _userInput;
            set { _userInput = value; OnPropertyChanged(); }
        }

        public string Username
        {
            get => _username;
        }

        public bool IsBusy
        {
            get => _isBusy;
            set { _isBusy = value; OnPropertyChanged(); ((RelayCommand)SendMessageCommand).RaiseCanExecuteChanged(); }
        }

        public Topic SelectedTopic
        {
            get => _selectedTopic;
            set
            {
                if (_selectedTopic != value)
                {
                    _selectedTopic = value;
                    OnPropertyChanged();
                    LoadMessagesForTopic();
                }
            }
        }

        public ICommand SendMessageCommand => _sendMessageCommand ??= new RelayCommand(SendMessage, () => !IsBusy && !string.IsNullOrWhiteSpace(UserInput) && SelectedTopic != null);
        public ICommand NewTopicCommand => _newTopicCommand ??= new RelayCommand(CreateNewTopic);
        public ICommand DeleteTopicCommand => _deleteTopicCommand ??= new RelayCommand<Topic>(DeleteTopic);
        public ICommand EditTopicCommand => _editTopicCommand ??= new RelayCommand<Topic>(EditTopic);
        public ICommand CopyCommand => new RelayCommand<ChatMessage>(CopyMessage);
        public ICommand EditCommand => new RelayCommand<ChatMessage>(EditMessage);
        public ICommand DeleteMessageCommand => new RelayCommand<ChatMessage>(DeleteMessage);
        public ICommand RegenerateCommand => new RelayCommand<ChatMessage>(RegenerateResponse);

        private void CopyMessage(ChatMessage message)
        {
            if (message != null)
            {
                System.Windows.Clipboard.SetText(message.Content);
            }
        }

        private async void EditMessage(ChatMessage message)
        {
            if (message == null || message.Role != "user") return;

            // 简单的实现：用一个输入框来获取新的内容
            var newContent = Microsoft.VisualBasic.Interaction.InputBox("编辑消息:", "编辑", message.Content);
            if (!string.IsNullOrEmpty(newContent) && newContent != message.Content)
            {
                message.Content = newContent;
                await _topicService.UpdateMessageAsync(message);
                LoadMessagesForTopic(); // 重新加载以刷新UI
            }
        }

        private async void DeleteMessage(ChatMessage message)
        {
            if (message == null) return;

            await _topicService.DeleteMessageAsync(message);
            Messages.Remove(message);
        }

        private async void RegenerateResponse(ChatMessage message)
        {
            if (message == null || message.Role != "assistant") return;

            var lastUserMessage = Messages.LastOrDefault(m => m.Role == "user" && m.Timestamp < message.Timestamp);
            if (lastUserMessage == null) return;

            IsBusy = true;
            await _topicService.DeleteMessageAsync(message);
            Messages.Remove(message);

            await GenerateAIResponse(lastUserMessage.Content);
            IsBusy = false;
        }

        private async Task LoadTopics()
        {
            var topics = await _topicService.GetTopicsAsync(_username);
            Topics.Clear();
            foreach (var topic in topics)
            {
                Topics.Add(topic);
            }
            if (Topics.Any())
            {
                SelectedTopic = Topics.First();
            }
        }

        private async void LoadMessagesForTopic()
        {
            if (SelectedTopic == null)
            {
                Messages.Clear();
                return;
            }

            Messages.Clear();
            var messages = await _topicService.GetMessagesByTopicAsync(SelectedTopic.Id);
            foreach (var message in messages)
            {
                message.SenderName = message.Role == "user" ? message.SenderName : "AI助手";
                message.Avatar = message.Role == "user" ? _avatar : "pack://application:,,,/uni-ai.png";

                // 解析消息中的图片URL
                message.ImageUrls = ExtractImageUrls(message.Content);

                Messages.Add(message);
            }
            _semanticKernelService.SetConversationHistory(messages);
        }

        private async Task SendMessage()
        {
            if (string.IsNullOrWhiteSpace(UserInput) || SelectedTopic == null)
                return;

            var userMessageContent = UserInput;

            // 检查是否是新话题的第一条消息，如果是则自动生成话题标题
            var isFirstMessage = Messages.Count == 0 && SelectedTopic.Name.StartsWith("新话题");

            var userMessage = new ChatMessage
            {
                Role = "user",
                SenderName = _username,
                Content = userMessageContent,
                Timestamp = DateTime.Now,
                TopicId = SelectedTopic.Id,
                Avatar = _avatar,
            };

            // 解析用户消息中的图片URL
            userMessage.ImageUrls = ExtractImageUrls(userMessageContent);

            // 调试输出
            System.Diagnostics.Debug.WriteLine($"用户消息内容: {userMessageContent}");
            System.Diagnostics.Debug.WriteLine($"提取到的图片URL数量: {userMessage.ImageUrls.Count}");
            foreach (var url in userMessage.ImageUrls)
            {
                System.Diagnostics.Debug.WriteLine($"  图片URL: {url}");
            }

            await _topicService.AddMessageAsync(userMessage);
            Messages.Add(userMessage);
            UserInput = string.Empty;

            IsBusy = true;

            // 如果是第一条消息，生成话题标题
            if (isFirstMessage)
            {
                try
                {
                    var topicTitle = await _semanticKernelService.GenerateTopicTitleAsync(userMessageContent);
                    SelectedTopic.Name = topicTitle;
                    await _topicService.UpdateTopicAsync(SelectedTopic);
                }
                catch (Exception)
                {
                    // 如果生成标题失败，使用用户消息的前15个字符作为标题
                    var fallbackTitle = userMessageContent.Length > 15 ? userMessageContent.Substring(0, 15) + "..." : userMessageContent;
                    SelectedTopic.Name = fallbackTitle;
                    await _topicService.UpdateTopicAsync(SelectedTopic);
                }
            }

            await GenerateAIResponse(userMessageContent);

            IsBusy = false;
        }

        private async Task GenerateAIResponse(string userMessage)
        {
            var aiResponseContent = await _semanticKernelService.GetChatResponseAsync(userMessage);

            var aiMessage = new ChatMessage
            {
                Role = "assistant",
                SenderName = "AI助手",
                Content = aiResponseContent,
                Timestamp = DateTime.Now,
                TopicId = SelectedTopic.Id,
                Avatar = "pack://application:,,,/uni-ai.png"
            };

            // 解析AI响应中的图片URL
            aiMessage.ImageUrls = ExtractImageUrls(aiResponseContent);

            // 调试输出
            System.Diagnostics.Debug.WriteLine($"AI响应内容: {aiResponseContent}");
            System.Diagnostics.Debug.WriteLine($"提取到的图片URL数量: {aiMessage.ImageUrls.Count}");
            foreach (var url in aiMessage.ImageUrls)
            {
                System.Diagnostics.Debug.WriteLine($"  图片URL: {url}");
            }

            await _topicService.AddMessageAsync(aiMessage);
            Messages.Add(aiMessage);
        }

        private async Task CreateNewTopic()
        {
            var newTopic = await _topicService.CreateTopicAsync("新话题 " + DateTime.Now.ToString("HH:mm:ss"), _username);
            Topics.Insert(0, newTopic);
            SelectedTopic = newTopic;
        }

        private async Task DeleteTopic(Topic topic)
        {
            if (topic == null) return;

            await _topicService.DeleteTopicAsync(topic);
            Topics.Remove(topic);

            if (SelectedTopic == topic)
            {
                SelectedTopic = Topics.FirstOrDefault();
            }
        }

        private async Task EditTopic(Topic topic)
        {
            if (topic == null) return;

            // 使用输入框来获取新的话题名称
            var newName = Microsoft.VisualBasic.Interaction.InputBox("编辑话题名称:", "编辑话题", topic.Name);
            if (!string.IsNullOrEmpty(newName) && newName != topic.Name)
            {
                topic.Name = newName;
                await _topicService.UpdateTopicAsync(topic);
                // 由于Topic实现了INotifyPropertyChanged，UI会自动更新
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 从消息内容中提取图片URL
        /// 支持多种格式：Markdown图片语法、HTML img标签、直接URL等
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <returns>图片URL列表</returns>
        private List<string> ExtractImageUrls(string content)
        {
            var imageUrls = new List<string>();

            if (string.IsNullOrWhiteSpace(content))
                return imageUrls;

            try
            {
                // 1. Markdown图片语法: ![alt](url)
                var markdownPattern = @"!\[.*?\]\((https?://[^\s\)]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s\)]*)?)\)";
                var markdownMatches = System.Text.RegularExpressions.Regex.Matches(content, markdownPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in markdownMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                    }
                }

                // 2. HTML img标签: <img src="url" />
                var htmlPattern = @"<img[^>]+src=[""']?(https?://[^\s""'>]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s""'>]*)?)[""']?[^>]*>";
                var htmlMatches = System.Text.RegularExpressions.Regex.Matches(content, htmlPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in htmlMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                    }
                }

                // 3. 直接的图片URL (独立一行或被空格包围)
                var directUrlPattern = @"(?:^|\s)(https?://[^\s]+(?:\.(?:jpg|jpeg|png|gif|bmp|webp|svg))(?:\?[^\s]*)?)(?:\s|$)";
                var directMatches = System.Text.RegularExpressions.Regex.Matches(content, directUrlPattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase |
                    System.Text.RegularExpressions.RegexOptions.Multiline);

                foreach (System.Text.RegularExpressions.Match match in directMatches)
                {
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                System.Diagnostics.Debug.WriteLine($"ExtractImageUrls error: {ex.Message}");
            }

            return imageUrls;
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Func<Task> execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = () => { execute(); return Task.CompletedTask; };
            _canExecute = canExecute;
        }
        public bool CanExecute(object parameter) => _canExecute == null || _canExecute();

        public async void Execute(object parameter) => await _execute();

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
    public class RelayCommand<T> : ICommand
    {
        private readonly Func<T, Task> _execute;
        private readonly Predicate<T> _canExecute;

        public RelayCommand(Func<T, Task> execute, Predicate<T> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }
        public RelayCommand(Action<T> execute, Predicate<T> canExecute = null)
        {
            _execute = (T param) => { execute(param); return Task.CompletedTask; };
            _canExecute = canExecute;
        }
        public bool CanExecute(object parameter) => _canExecute == null || _canExecute((T)parameter);

        public async void Execute(object parameter) => await _execute((T)parameter);

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
