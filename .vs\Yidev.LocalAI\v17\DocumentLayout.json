{"Version": 1, "WorkspaceRootPath": "D:\\yidev\\Source\\yidev.localai\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\services\\semantickernelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:services\\semantickernelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\mcpplugins\\filereaderplugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:mcpplugins\\filereaderplugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\models\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:models\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\models\\topic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:models\\topic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\converters\\booltoconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:converters\\booltoconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\services\\topicservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:services\\topicservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|d:\\yidev\\source\\yidev.localai\\services\\databasecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5532C3F8-9B67-4399-A9F9-499501744B42}|Yidev.LocalAI.csproj|solutionrelative:services\\databasecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\yidev\\Source\\yidev.localai\\src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\App.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\yidev\\Source\\yidev.localai\\McpPlugins\\FileWriterPlugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:McpPlugins\\FileWriterPlugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 12, "Title": "FileWriterPlugin.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\McpPlugins\\FileWriterPlugin.cs", "RelativeDocumentMoniker": "McpPlugins\\FileWriterPlugin.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\McpPlugins\\FileWriterPlugin.cs", "RelativeToolTip": "McpPlugins\\FileWriterPlugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:28:37.136Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainViewModel.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Models\\MainViewModel.cs", "RelativeDocumentMoniker": "Models\\MainViewModel.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Models\\MainViewModel.cs", "RelativeToolTip": "Models\\MainViewModel.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAAAMAAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:14:30.49Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SemanticKernelService.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Services\\SemanticKernelService.cs", "RelativeDocumentMoniker": "Services\\SemanticKernelService.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Services\\SemanticKernelService.cs", "RelativeToolTip": "Services\\SemanticKernelService.cs", "ViewState": "AgIAABMAAAAAAAAAAAAcwEsAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T01:46:13.536Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\App.xaml.cs", "RelativeDocumentMoniker": "App.xaml.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\App.xaml.cs", "RelativeToolTip": "App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T01:03:35.136Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "FileReaderPlugin.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\McpPlugins\\FileReaderPlugin.cs", "RelativeDocumentMoniker": "McpPlugins\\FileReaderPlugin.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\McpPlugins\\FileReaderPlugin.cs", "RelativeToolTip": "McpPlugins\\FileReaderPlugin.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAFYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T06:56:55.93Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T07:37:43.768Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Topic.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Models\\Topic.cs", "RelativeDocumentMoniker": "Models\\Topic.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Models\\Topic.cs", "RelativeToolTip": "Models\\Topic.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T09:02:35.467Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-22T07:44:05.079Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "BoolToConverter.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Converters\\BoolToConverter.cs", "RelativeDocumentMoniker": "Converters\\BoolToConverter.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Converters\\BoolToConverter.cs", "RelativeToolTip": "Converters\\BoolToConverter.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAmwMAAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T08:50:40.446Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "TopicService.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Services\\TopicService.cs", "RelativeDocumentMoniker": "Services\\TopicService.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Services\\TopicService.cs", "RelativeToolTip": "Services\\TopicService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T09:03:51.232Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "ChatMessage.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Models\\ChatMessage.cs", "RelativeDocumentMoniker": "Models\\ChatMessage.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Models\\ChatMessage.cs", "RelativeToolTip": "Models\\ChatMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T08:52:06.165Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "DatabaseContext.cs", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\Services\\DatabaseContext.cs", "RelativeDocumentMoniker": "Services\\DatabaseContext.cs", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\Services\\DatabaseContext.cs", "RelativeToolTip": "Services\\DatabaseContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T08:39:06.59Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "App.js", "DocumentMoniker": "D:\\yidev\\Source\\yidev.localai\\src\\App.js", "RelativeDocumentMoniker": "src\\App.js", "ToolTip": "D:\\yidev\\Source\\yidev.localai\\src\\App.js", "RelativeToolTip": "src\\App.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-06-14T08:37:03.38Z"}]}]}]}