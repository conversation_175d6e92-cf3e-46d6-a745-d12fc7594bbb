# 图片显示功能测试示例

## 测试用例

### 1. Markdown格式图片

在聊天中发送以下消息来测试Markdown格式的图片显示：

```
请看这张图片：
![示例图片](https://picsum.photos/300/200?random=1)

这是另一张图片：
![风景图片](https://picsum.photos/400/300?random=2)
```

### 2. HTML格式图片

测试HTML img标签格式：

```
这是HTML格式的图片：
<img src="https://picsum.photos/350/250?random=3" alt="HTML图片" />

多个HTML图片：
<img src="https://picsum.photos/200/200?random=4" />
<img src="https://picsum.photos/250/200?random=5" />
```

### 3. 直接URL格式

测试直接图片URL：

```
直接图片链接：
https://picsum.photos/300/300?random=6

多个直接链接：
https://picsum.photos/280/200?random=7
https://picsum.photos/320/240?random=8
```

### 4. 混合格式

测试混合使用多种格式：

```
这里有多种格式的图片：

Markdown格式：
![图片1](https://picsum.photos/200/150?random=9)

HTML格式：
<img src="https://picsum.photos/250/180?random=10" alt="图片2" />

直接URL：
https://picsum.photos/300/200?random=11

文本和图片混合显示效果很好！
```

### 5. 测试图片加载失败

测试无效URL的处理：

```
这是一个无效的图片URL：
![无效图片](https://invalid-url.com/nonexistent.jpg)

这个URL也不存在：
https://example.com/fake-image.png
```

## 预期效果

1. **正常显示**: 有效的图片URL应该正确显示图片
2. **布局美观**: 多张图片应该水平排列，自动换行
3. **交互效果**: 鼠标悬停时图片应该轻微放大
4. **错误处理**: 无效URL应该显示占位符而不是破坏界面

## 测试步骤

1. 启动应用程序
2. 创建新的对话
3. 复制上述测试用例中的任一消息
4. 发送消息并观察图片显示效果
5. 测试鼠标悬停效果
6. 验证图片与文本的布局

## 注意事项

- 使用的测试图片来自 picsum.photos，这是一个提供随机图片的免费服务
- 图片URL中的 `?random=数字` 参数确保每次获取不同的图片
- 测试时需要确保网络连接正常
- 如果某些图片加载较慢，请耐心等待

## AI助手响应测试

可以要求AI助手生成包含图片的响应，例如：

```
请生成一个包含图片的回复，使用Markdown格式显示一张风景图片。
```

AI助手应该能够在回复中包含图片URL，并且图片会自动显示在消息中。
